import { useState } from "react";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CustomError } from "@/utils/custom-error";
import { getAddressCreationFormSchema } from "../../validation/schemas/address-creation";
import createAddressOnServerSide from "../../services/addresses/address-creation";
import useAddressSelection from "../../store/address-selection-store";
import useCountries from "./use-countries";
import useCountryCities from "./use-country-cities";
import parsePhoneNumberFromString from "libphonenumber-js";
import { refreshToken } from "@/modules/auth/services/refresh-token";

interface UseAddressCreationFormParams {
  useAddressIdInCheckout?: boolean;
  onSuccess?: () => void;
}

export default function useAddressCreationForm({
  useAddressIdInCheckout = false,
  onSuccess,
}: UseAddressCreationFormParams = {}) {
  const queryClient = useQueryClient();
  const t = useTranslations("modules.checkout.validations");
  const errorsContent = useTranslations("modules.checkout.errors");

  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState("");

  const { country, countryOptionnalLabels } = useCountries();
  const { city } = useCountryCities({
    countryCode: country?.code as string,
  });

  const selectAddressForCheckout = useAddressSelection(
    (store) => store.selectAddress
  );

  const formSchema = getAddressCreationFormSchema(t);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      firstName: "",
      lastName: "",
      address1: "",
      address2: "",
      company: "",
      province: "",
      zip: "",
      phone: "",
      city: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (error !== "") setError("");
    setIsPending(true);

    // phone number
    if (country) {
      const phoneNumber = `+${country.phoneNumberPrefix}${values.phone}`;
      if (!parsePhoneNumberFromString(phoneNumber)?.isValid()) {
        form.setError("phone", {
          type: "manual",
          message: t ? t("phone.invalid") : "Numéro de téléphone invalide.",
        });
        setIsPending(false);
        return;
      }
    }

    const addressData = {
      email: values.email,
      firstName: values.firstName,
      lastName: values.lastName,
      address1: values.address1,
      address2: values.address2 || "",
      company: values.company || "",
      province: values.province || "",
      zip: values.zip || "",
      phone: values.phone,
      cityId: country?.code === "TN" ? city.code : values.city,
    };

    try {
      const res = await createAddressOnServerSide(addressData);

      queryClient.invalidateQueries({
        queryKey: ["user-address"],
        exact: false,
      });

      form.reset();

      if (useAddressIdInCheckout && res.address) {
        selectAddressForCheckout(res.address.id);
      }

      if (onSuccess) {
        onSuccess();
      }

      if (!useAddressIdInCheckout) {
        document.body.scrollIntoView({ behavior: "smooth" });
      }
    } catch (e) {
      const error = e as CustomError;

      if (error.status === 401) {
        const res = await refreshToken(() =>
          createAddressOnServerSide(addressData)
        );

        if (!res) setError(errorsContent("unauthorized"));
      }
      if (error.status === 400) {
        setError(errorsContent("invalidData"));
      } else if (error.status === 401) {
        setError(errorsContent("unauthorized"));
      } else if (error.code === "emailInvalid") {
        setError(errorsContent("emailInvalid"));
      } else {
        setError(errorsContent("technicalIssue"));
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    error,
    onSubmit,
    isPending,
    form,
    country,
    countryOptionnalLabels,
  };
}
