"use client";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import ProductContainer from "@/modules/cart/components/product-container";
import { useCartStore } from "@/modules/cart/store/cart-store";
import usePrices from "@/modules/cart/hooks/use-prices";
import usePricePointsConverter from "@/modules/points-system/hook/use-price-points-converter";
import CouponCodeInput from "../../coupon-codes/components/coupon-code-input";
import DetailedCouponBreakdown from "../../coupon-codes/components/detailed-coupon-breakdown";
import useCurrency from "@/modules/catalog/hooks/use-currency";

export default function OrderSummary() {
  const { cartItems } = useCartStore((store) => store.state);
  const t = useTranslations("checkoutPage.orderSummary");
  const { subtotal, shipping, total } = usePrices();
  const { points: pointsToWin } = usePricePointsConverter(subtotal);
  const { currency } = useCurrency();

  return (
    <div className="rounded-2xl bg-primary-muted p-6 flex flex-col space-y-7">
      <h2 className="font-bold text-center regularL:text-start">
        <Text textStyle="TS4">{t("heading")}</Text>
      </h2>
      {/* Points Banner */}

      <div className="bg-primary rounded-2xl text-white p-4 flex items-center gap-2">
        <p className="">
          <Text textStyle="TS6">
            {t.rich("pointsBanner", {
              points: (_) => (
                <span className="font-bold">{pointsToWin.toFixed(3)}</span>
              ),
              strong: (chunk) => <span className="font-bold">{chunk}</span>,
            })}
          </Text>
        </p>
      </div>

      {/* Cart Items */}
      <div className="flex flex-col space-y-4">
        <h3 className="font-medium">
          <Text textStyle="TS7">{t("cartItemsTitle")}</Text>
        </h3>
        {cartItems.map((item) => (
          <ProductContainer key={item.id} productItem={item} />
        ))}
      </div>

      {/* Coupon Code */}
      <Separator />
      <CouponCodeInput />
      <Separator />

      {/* Order Summary */}
      <div className="space-y-4 text-black">
        <Text textStyle="TS6" className="flex justify-between items-center">
          <span className="">{t("orderSummary.subtotal")}</span>
          <span>{`${subtotal.toFixed(3)} ${currency}`}</span>
        </Text>

        <DetailedCouponBreakdown />

        {
          <Text textStyle="TS6" className="flex justify-between items-center">
            <span className="">{t("orderSummary.shipping")}</span>
            <span className="">{`${shipping.toFixed(3)} ${currency}`}</span>
          </Text>
        }

        <Separator />
        <Text
          textStyle="TS5"
          className="flex justify-between items-center font-bold"
        >
          <span>{t("orderSummary.total")}</span>
          <span>{`${total.toFixed(3)} ${currency}`}</span>
        </Text>
      </div>
    </div>
  );
}
