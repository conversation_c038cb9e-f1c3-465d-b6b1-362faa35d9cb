"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import PaymentOptionsSelection from "./payment-options-seletion";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import useCheckoutSubmission from "../hooks/use-checkout-submission";
import { useCheckoutStore } from "../store/checkout-store";
import AddressesSelection from "./addresses/addresses-selection";
import useAddressSelection from "../store/address-selection-store";
import useUserStore from "@/modules/auth/store/user-store";
import { useAuthDialogState } from "@/modules/auth/store/auth-dialog-state-store";
import { Skeleton } from "@/components/ui/skeleton";
import AddressCreationForm from "./addresses/form/address-creation-form";
import useAddressCreationForm from "../hooks/addresses/use-address-creation-form";

export default function CheckoutForm() {
  const t = useTranslations("checkoutPage");

  const { user, isLoading: userIsLoading } = useUserStore((store) => store);
  const { onSubmit } = useAddressCreationForm({
    useAddressIdInCheckout: true,
  });

  const { isLoading, checkoutOnServerSide, error } =
    useCheckoutSubmission(user);
  const { notes, setNotes } = useCheckoutStore();
  const selectedAddressId = useAddressSelection(
    (store) => store.selectedAddressId
  );
  const setAuthDialogIsOpen = useAuthDialogState((store) => store.setIsOpen);

  const clickToCheckout = async () => {
    if (user && user.isAuthenticated) {
      if (selectedAddressId === "") {
        alert("Please create an address first");
        return;
      } else {
        checkoutOnServerSide(null, notes, selectedAddressId);
      }
    } else {
      checkoutOnServerSide(onSubmit(), notes, selectedAddressId);
    }
  };

  return (
    <div className="w-full flex flex-col space-y-6 text-primary p-5">
      {!userIsLoading ? (
        (!user || (user && !user.isAuthenticated)) && (
          <div className="w-full px-4 rounded-2xl bg-primary-muted flex justify-center py-5 text-center text-black">
            <Text textStyle="TS4">
              {t.rich("loginToFasterPayment", {
                link: (chunk) => (
                  <Button
                    variant={"ghost"}
                    className="px-0 underline underline-offset-[5px] decoration-[1.5px]"
                    onClick={() => setAuthDialogIsOpen(true)}
                  >
                    <Text textStyle="TS4">{chunk}</Text>
                  </Button>
                ),
              })}
            </Text>
          </div>
        )
      ) : (
        <div className="w-full px-4 rounded-2xl bg-primary-muted flex flex-col items-center space-y-2 py-5 text-center text-black">
          {Array.from({ length: 2 }).map((_, idx) => (
            <Skeleton
              key={idx}
              className={cn("h-4 w-[80%] min-w-[150px] bg-gray bg-opacity-50", {
                "w-[calc(80%-100px)] min-w-[130px]": idx === 1,
              })}
            />
          ))}
        </div>
      )}
      <section className="flex flex-col space-y-4">
        <h3>
          <Text textStyle="TS4">{t("form.address.title")}</Text>
        </h3>
        <AddressesSelection />
        {selectedAddressId === "" && (
          <AddressCreationForm useAddressIdInCheckout={true} />
        )}
      </section>
      <Separator />
      <section className="flex flex-col space-y-4">
        <h3>
          <Text textStyle="TS4">{t("form.deliveryNote.title")}</Text>
        </h3>
        <Textarea
          id="deliveryNote"
          className="L:min-h-[128px] border border-primary"
          placeholder={t("form.deliveryNote.inputPlaceholder")}
          value={notes}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
            setNotes(e.target.value)
          }
        />
      </section>
      <Separator />
      <section className="flex flex-col space-y-4">
        <h3>
          <Text textStyle="TS4">{t("form.payment.title")}</Text>
        </h3>
        <PaymentOptionsSelection />
      </section>
      <Separator />
      <Text textStyle="TS7" className="text-danger text-center">
        {error}
      </Text>
      <Button
        className={cn("w-full h-[50px] rounded-xl", {
          "scale-95": isLoading,
        })}
        onClick={clickToCheckout}
        disabled={isLoading}
      >
        <Text textStyle="TS5">{t("form.buttons.confirm")}</Text>
      </Button>
    </div>
  );
}
