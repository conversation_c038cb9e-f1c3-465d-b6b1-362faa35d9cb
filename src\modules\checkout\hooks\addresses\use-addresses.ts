import { useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { retrieveUserAddresses } from "../../services/addresses/addresses-extraction";
import useBackendLocale from "@/hooks/use-backend-locale";

export default function useAddresses() {
  const { backendLocale } = useBackendLocale();
  const pathname = usePathname();
  const { data, isLoading } = useQuery({
    queryKey: ["user-address", backendLocale],
    queryFn: () => retrieveUserAddresses({ locale: backendLocale }),
    enabled: ![
      "mon-compte",
      "mon-compte/commandes",
      "mon-compte/settings",
    ].every((disabledPathname) => pathname.endsWith(disabledPathname)), //data in those pages should not be fetched
  });

  return {
    addresses: data ? data : null,
    addressesAreLoading: isLoading,
  };
}
