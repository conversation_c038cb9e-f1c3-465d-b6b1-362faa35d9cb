"use client";

import { useTranslations } from "next-intl";
import type React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import useAddressCreationForm from "../../../hooks/addresses/use-address-creation-form";
import useCountryCities from "../../../hooks/addresses/use-country-cities";
import PhoneNumberInput from "@/components/ui/phone-number-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AddressCreationFormProps extends React.ComponentProps<"form"> {
  useAddressIdInCheckout?: boolean;
  onSuccess?: () => void;
  formIsHidden?: boolean;
}

export default function AddressCreationForm({
  className,
  useAddressIdInCheckout = false,
  onSuccess,
  formIsHidden = false,
  ...props
}: AddressCreationFormProps) {
  const t = useTranslations("checkoutPage.form");
  const buttonsContent = useTranslations("accountPage.accountAdress");

  const { error, onSubmit, isPending, form, country, countryOptionnalLabels } =
    useAddressCreationForm({
      useAddressIdInCheckout,
      onSuccess,
    });

  const { cities, city, changeCity } = useCountryCities({
    countryCode: country?.code || "TN",
  });

  if (!country) {
    return null;
  }

  return (
    <Form {...form}>
      <form
        id="addressCreation"
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn("flex flex-col gap-6", className, {
          hidden: formIsHidden,
        })}
        {...props}
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <h2 className="text-xl font-bold">{t("address.title")}</h2>
          <p className="text-muted-foreground text-sm text-balance">
            {t("address.description")}
          </p>
        </div>

        <div className="grid gap-6">
          {/* Email Field */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="email">
                  {t("address.labels.email")} *
                </FormLabel>
                <FormControl>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("address.labels.email")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {country.formatting.show.split("_").map((inputs, idx) => {
            const inputsLabels = inputs.split(" ");

            return (
              <div key={idx} className="flex gap-4">
                {inputsLabels.map((input) => {
                  const fieldName = input.substring(1, input.length - 1);
                  const isOptional = countryOptionnalLabels.includes(fieldName);

                  if (fieldName === "country") {
                    return null;
                  }

                  if (
                    fieldName === "city" &&
                    country.code === "TN" &&
                    cities.length > 0
                  ) {
                    return (
                      <div key={fieldName} className="flex-1">
                        <FormField
                          control={form.control}
                          name="city"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {t(`address.labels.${fieldName}`)}
                                {!isOptional && " *"}
                              </FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  changeCity(value);
                                  field.onChange(value);
                                }}
                                value={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue
                                      placeholder={t(
                                        `address.labels.${fieldName}`
                                      )}
                                    />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {cities.map((cityItem) => (
                                    <SelectItem
                                      key={cityItem.code}
                                      value={cityItem.code}
                                    >
                                      {cityItem.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    );
                  }

                  if (fieldName === "phone") {
                    return (
                      <div key={fieldName} className="flex-1">
                        <FormField
                          control={form.control}
                          name="phone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {t(`address.labels.${fieldName}`)}
                                {!isOptional && " *"}
                              </FormLabel>
                              <FormControl>
                                <PhoneNumberInput
                                  inputName="phone"
                                  code={`+${country.phoneNumberPrefix}`}
                                  value={field.value}
                                  onChange={field.onChange}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    );
                  }

                  return (
                    <div key={fieldName} className="flex-1">
                      <FormField
                        control={form.control}
                        name={fieldName as any}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t(`address.labels.${fieldName}`)}
                              {!isOptional && " *"}
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={`${t(
                                  `address.labels.${fieldName}`
                                )}${
                                  isOptional ? ` ${t("address.optional")}` : ""
                                }`}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  );
                })}
              </div>
            );
          })}

          {error && (
            <p className="text-sm text-destructive text-center">{error}</p>
          )}

          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending
              ? buttonsContent("buttons.saving")
              : buttonsContent("buttons.save")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
