import { TranslateFunction } from "@/types";
import { z } from "zod";

export function getSignUpFormSchema(t?: TranslateFunction) {
  return z.object({
    firstName: z.string().min(1, {
      message: t ? t("firstName.required") : "Le prénom est requis.",
    }),
    lastName: z.string().min(1, {
      message: t ? t("lastName.required") : "Le nom de famille est requis.",
    }),
    email: z
      .string()
      .min(1, {
        message: t ? t("email.required") : "L'adresse e-mail est requise.",
      })
      .email(t ? t("email.invalid") : "Format d'adresse e-mail invalide."),
    password: z.string().min(8, {
      message: t
        ? t("password.tooShort", { min: 8 })
        : "Le mot de passe doit contenir au moins 8 caractères.",
    }),
  });
}
